#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
精简文档处理模块
只保留核心功能：加载和分割文档
"""

from typing import List
from pathlib import Path

from langchain_community.document_loaders import PyPDFLoader, TextLoader
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain_core.documents import Document

from config import CHUNK_SIZE, CHUNK_OVERLAP, SUPPORTED_EXTENSIONS


class DocumentProcessor:
    """精简文档处理器 - 只保留核心功能"""

    def __init__(self, chunk_size: int = CHUNK_SIZE, chunk_overlap: int = CHUNK_OVERLAP):
        """初始化文档处理器"""
        self.text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=chunk_size,
            chunk_overlap=chunk_overlap
        )
    

    
    def process_documents(self, file_paths: List[str]) -> List[Document]:
        """处理文档列表，返回分割后的文档块"""
        all_docs = []

        for file_path in file_paths:
            try:
                # 验证文件
                path = Path(file_path)
                if not path.exists():
                    print(f"⚠️  文件不存在: {file_path}")
                    continue

                extension = path.suffix.lower()
                if extension not in SUPPORTED_EXTENSIONS:
                    print(f"⚠️  不支持的文件格式: {extension}")
                    continue

                print(f"正在处理: {path.name}")

                # 加载文档
                if extension == '.pdf':
                    loader = PyPDFLoader(str(path))
                else:  # .txt, .md
                    loader = TextLoader(str(path), encoding='utf-8')

                documents = loader.load()

                # 分割文档
                docs = self.text_splitter.split_documents(documents)
                all_docs.extend(docs)

                print(f"✅ 已处理: {len(docs)} 个文档块")

            except Exception as e:
                print(f"❌ 处理文件 {file_path} 时出错: {e}")
                continue

        print(f"🎯 总计处理: {len(all_docs)} 个文档块")
        return all_docs
