#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
精简向量数据库管理模块
只保留核心功能：创建、加载、删除向量数据库
"""

import os
import json
from typing import List, Optional

from langchain_huggingface import HuggingFaceEmbeddings
from langchain_community.vectorstores import FAISS
from langchain_core.documents import Document

from config import VECTOR_STORE_DIR, METADATA_DIR, EMBEDDING_MODEL_NAME, DEFAULT_VECTOR_STORE_NAME


class VectorStoreManager:
    """精简向量数据库管理器 - 只保留核心功能"""

    _embeddings_cache = {}  # 类级别的嵌入模型缓存

    def __init__(self, store_name: str = DEFAULT_VECTOR_STORE_NAME,
                 embedding_model: str = EMBEDDING_MODEL_NAME):
        """初始化向量数据库管理器"""
        self.store_name = store_name
        self.embedding_model_name = embedding_model

        # 使用缓存的嵌入模型
        if embedding_model not in self._embeddings_cache:
            self._embeddings_cache[embedding_model] = HuggingFaceEmbeddings(model_name=embedding_model)
        self.embeddings = self._embeddings_cache[embedding_model]

        # 路径设置
        self.store_path = os.path.join(VECTOR_STORE_DIR, store_name)
        self.metadata_path = os.path.join(METADATA_DIR, f"{store_name}.json")

    def exists(self) -> bool:
        """检查向量数据库是否存在"""
        return (os.path.exists(os.path.join(self.store_path, "index.faiss")) and
                os.path.exists(os.path.join(self.store_path, "index.pkl")))
    
    def _save_basic_metadata(self, total_chunks: int, total_files: int) -> None:
        """保存基本元数据"""
        metadata = {
            "store_name": self.store_name,
            "total_chunks": total_chunks,
            "total_files": total_files
        }

        try:
            with open(self.metadata_path, 'w', encoding='utf-8') as f:
                json.dump(metadata, f, ensure_ascii=False, indent=2)
        except Exception:
            pass  # 元数据保存失败不影响核心功能

    def get_info(self) -> Optional[dict]:
        """获取基本信息"""
        if not os.path.exists(self.metadata_path):
            return None

        try:
            with open(self.metadata_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception:
            return None
    
    def create_vector_store(self, documents: List[Document],
                           file_count: int = 1) -> FAISS:
        """创建新的向量数据库"""
        print("正在创建向量数据库...")

        # 创建向量数据库
        vector_store = FAISS.from_documents(documents, self.embeddings)

        # 保存向量数据库
        vector_store.save_local(self.store_path)
        print(f"向量数据库已保存: {len(documents)} 个文档块")

        # 保存基本元数据
        self._save_basic_metadata(len(documents), file_count)

        return vector_store
    
    def load_vector_store(self) -> Optional[FAISS]:
        """加载现有的向量数据库"""
        if not self.exists():
            return None

        try:
            print("正在加载向量数据库...")
            vector_store = FAISS.load_local(
                self.store_path,
                self.embeddings,
                allow_dangerous_deserialization=True
            )

            # 显示基本信息
            info = self.get_info()
            if info:
                print(f"已加载向量数据库: {info.get('total_chunks', 0)} 个文档块")
            else:
                print("向量数据库加载成功")

            return vector_store
        except Exception as e:
            print(f"加载向量数据库失败: {e}")
            return None
    
    def delete_vector_store(self) -> bool:
        """删除向量数据库和相关文件"""
        try:
            # 删除向量数据库文件
            import shutil
            if os.path.exists(self.store_path):
                shutil.rmtree(self.store_path)

            # 删除元数据文件
            if os.path.exists(self.metadata_path):
                os.remove(self.metadata_path)

            print(f"向量数据库 {self.store_name} 已删除")
            return True
        except Exception as e:
            print(f"删除向量数据库失败: {e}")
            return False
