#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RAG 系统配置文件
"""

import os

# 基础路径配置
BASE_DIR = os.path.dirname(os.path.abspath(__file__))
VECTOR_STORE_DIR = os.path.join(BASE_DIR, "vector_stores")
METADATA_DIR = os.path.join(BASE_DIR, "metadata")

# 确保目录存在
os.makedirs(VECTOR_STORE_DIR, exist_ok=True)
os.makedirs(METADATA_DIR, exist_ok=True)

# 文档分割配置
CHUNK_SIZE = 500
CHUNK_OVERLAP = 50

# 嵌入模型配置
EMBEDDING_MODEL_NAME = "all-MiniLM-L6-v2"

# LLM 配置
OPENROUTER_API_KEY = "sk-or-v1-你的API密钥"  # 替换为你的真实API密钥
OPENROUTER_MODEL = "meta-llama/llama-3.2-3b-instruct:free"

# 向量数据库配置
DEFAULT_VECTOR_STORE_NAME = "default_store"

# 支持的文档格式
SUPPORTED_EXTENSIONS = {
    '.pdf': 'pdf',
    '.txt': 'text',
    '.md': 'text'
}

# RAG 提示模板
RAG_PROMPT_TEMPLATE = """请根据以下上下文来回答问题。如果上下文没有提供足够的信息，请说明你无法回答。

上下文: {context}

问题: {question}

答案:"""
