#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
精简文档管理工具
只保留核心功能：处理文档、列出数据库、删除数据库
"""

import argparse
import sys
import os
import json

from document_processor import DocumentProcessor
from vector_store_manager import VectorStoreManager


def validate_files(file_paths):
    """验证文件是否存在"""
    valid_files = []
    for file_path in file_paths:
        if os.path.exists(file_path):
            valid_files.append(file_path)
        else:
            print(f"❌ 文件不存在: {file_path}")
    return valid_files


def confirm_action(message):
    """确认操作"""
    response = input(f"{message} (y/n): ").lower()
    return response == 'y'


def process_documents(file_paths, store_name="default_store", force=False):
    """处理文档并创建向量数据库"""
    print(f"=== 文档处理工具 ===")
    print(f"向量数据库: {store_name}")

    # 验证文件
    valid_files = validate_files(file_paths)
    if not valid_files:
        print("❌ 没有有效的文件可处理")
        return False

    # 初始化组件
    processor = DocumentProcessor()
    manager = VectorStoreManager(store_name)

    # 检查是否需要重新创建
    if not force and manager.exists():
        print("⚠️  向量数据库已存在，使用 --force 强制重新创建")
        return True

    try:
        # 处理文档
        docs = processor.process_documents(valid_files)
        if not docs:
            print("❌ 没有成功处理任何文档")
            return False

        # 创建向量数据库
        manager.create_vector_store(docs, len(valid_files))

        print(f"✅ 处理完成! 向量数据库: {store_name}")
        return True

    except Exception as e:
        print(f"❌ 处理失败: {e}")
        return False


def list_stores():
    """列出所有向量数据库"""
    from config import METADATA_DIR

    print("=== 向量数据库列表 ===")

    if not os.path.exists(METADATA_DIR):
        print("没有找到任何向量数据库")
        return

    metadata_files = [f for f in os.listdir(METADATA_DIR) if f.endswith('.json')]

    if not metadata_files:
        print("没有找到任何向量数据库")
        return

    for metadata_file in metadata_files:
        store_name = metadata_file.replace('.json', '')
        metadata_path = os.path.join(METADATA_DIR, metadata_file)

        try:
            with open(metadata_path, 'r', encoding='utf-8') as f:
                metadata = json.load(f)

            print(f"\n📁 {store_name}")
            print(f"   文档块: {metadata.get('total_chunks', 0)}")
            print(f"   文件数: {metadata.get('total_files', 0)}")

        except Exception as e:
            print(f"❌ 读取 {store_name} 失败: {e}")


def delete_store(store_name):
    """删除向量数据库"""
    manager = VectorStoreManager(store_name)

    if not manager.exists():
        print(f"❌ 向量数据库 '{store_name}' 不存在")
        return False

    if confirm_action(f"删除向量数据库: {store_name}"):
        return manager.delete_vector_store()
    else:
        print("取消删除")
        return False


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="文档管理工具")
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # 处理文档命令
    process_parser = subparsers.add_parser('process', help='处理文档')
    process_parser.add_argument('files', nargs='+', help='要处理的文件路径')
    process_parser.add_argument('--store', default='default_store', help='向量数据库名称')
    process_parser.add_argument('--force', action='store_true', help='强制重新处理')
    
    # 列出数据库命令
    subparsers.add_parser('list', help='列出所有向量数据库')

    # 删除数据库命令
    delete_parser = subparsers.add_parser('delete', help='删除向量数据库')
    delete_parser.add_argument('store_name', help='要删除的向量数据库名称')
    
    args = parser.parse_args()
    
    if args.command == 'process':
        success = process_documents(args.files, args.store, args.force)
        sys.exit(0 if success else 1)
    
    elif args.command == 'list':
        list_stores()
    
    elif args.command == 'delete':
        success = delete_store(args.store_name)
        sys.exit(0 if success else 1)
    
    else:
        parser.print_help()


if __name__ == "__main__":
    main()
