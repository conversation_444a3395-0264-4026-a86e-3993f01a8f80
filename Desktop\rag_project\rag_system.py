#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
精简 RAG 系统主程序
只保留核心功能：问答查询
"""

from langchain_openai import ChatOpenAI
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.output_parsers import StrOutputParser

from vector_store_manager import VectorStoreManager
from config import OPENROUTER_API_KEY, OPENROUTER_MODEL, RAG_PROMPT_TEMPLATE, DEFAULT_VECTOR_STORE_NAME


class RAGSystem:
    """RAG 系统"""

    def __init__(self, store_name: str = DEFAULT_VECTOR_STORE_NAME):
        """初始化 RAG 系统"""
        self.store_name = store_name
        self.vector_store_manager = VectorStoreManager(store_name)
        self.vector_store = None
        self.llm = None
        self.rag_chain = None

        # 初始化组件
        self._init_llm()
        self._load_vector_store()
    
    def _init_llm(self):
        """初始化大语言模型"""
        try:
            self.llm = ChatOpenAI(
                model=OPENROUTER_MODEL,
                openai_api_base="https://openrouter.ai/api/v1",
                openai_api_key=OPENROUTER_API_KEY,
                temperature=0.1
            )
            print(f"✅ LLM 初始化成功: {OPENROUTER_MODEL}")
        except Exception as e:
            print(f"❌ LLM 初始化失败: {e}")
            print("请检查 config.py 中的 OPENROUTER_API_KEY 是否正确")
            exit(1)

    def _load_vector_store(self):
        """加载向量数据库"""
        self.vector_store = self.vector_store_manager.load_vector_store()
        if self.vector_store:
            self._setup_rag_chain()

    def _setup_rag_chain(self):
        """设置 RAG 链"""
        if self.vector_store is None:
            return

        def format_docs(docs):
            return "\n\n".join(doc.page_content for doc in docs)

        prompt = ChatPromptTemplate.from_template(RAG_PROMPT_TEMPLATE)

        self.rag_chain = (
            {"context": lambda x: format_docs(x["retrieved_docs"]), "question": lambda x: x["question"]}
            | prompt
            | self.llm
            | StrOutputParser()
        )
        print("✅ RAG 链设置完成")
    

    
    def query(self, question: str, top_k: int = 4) -> str:
        """查询 RAG 系统"""
        if self.vector_store is None:
            return "❌ 向量数据库未加载，请先使用 document_manager.py 处理文档"

        if self.rag_chain is None:
            return "❌ RAG 链未设置"

        try:
            # 检索相关文档
            retrieved_docs = self.vector_store.similarity_search(question, k=top_k)

            if not retrieved_docs:
                return "抱歉，我在文档中没有找到相关信息来回答您的问题。"

            # 生成回答
            response = self.rag_chain.invoke({
                "retrieved_docs": retrieved_docs,
                "question": question
            })

            return response

        except Exception as e:
            return f"❌ 查询时出错: {e}"


def main():
    """主函数"""
    print("=== 精简 RAG 系统 ===")

    # 初始化 RAG 系统
    rag_system = RAGSystem()

    # 检查向量数据库
    if rag_system.vector_store is None:
        print("\n❌ 没有找到向量数据库")
        print("请先使用以下命令处理文档:")
        print("python document_manager.py process 'your_document.pdf'")
        return

    # 交互式查询
    print("\n=== 开始问答 ===")
    print("输入问题进行查询，输入 'quit' 退出")

    while True:
        try:
            user_input = input("\n🤖 请输入问题: ").strip()

            if user_input.lower() in ['quit', 'exit', 'q']:
                print("👋 再见！")
                break
            elif user_input:
                print("🔍 正在查询...")
                response = rag_system.query(user_input)
                print(f"\n💡 回答: {response}")
            else:
                print("⚠️  请输入有效的问题")

        except KeyboardInterrupt:
            print("\n\n👋 再见！")
            break
        except Exception as e:
            print(f"❌ 出错: {e}")


if __name__ == "__main__":
    main()
