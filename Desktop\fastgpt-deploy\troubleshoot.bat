@echo off
echo ========================================
echo     FastGPT 故障诊断工具
echo ========================================
echo.

echo 1. 检查容器状态...
docker-compose ps
echo.

echo 2. 检查 aiproxy_pg 容器日志...
echo ----------------------------------------
docker-compose logs aiproxy_pg
echo ----------------------------------------
echo.

echo 3. 检查端口占用情况...
netstat -ano | findstr :5432
echo.

echo 4. 检查 Docker 资源使用...
docker system df
echo.

echo 5. 检查数据目录...
if exist "aiproxy_pg" (
    echo aiproxy_pg 数据目录存在
    dir aiproxy_pg
) else (
    echo aiproxy_pg 数据目录不存在
)
echo.

echo ========================================
echo 诊断完成，请查看上述信息
echo ========================================
pause
