# FastGPT Docker 部署指南

## 前置要求

1. **确保 Docker Desktop 已安装并运行**
   - 双击桌面上的 "Docker Desktop" 快捷方式启动
   - 等待 Docker Desktop 完全启动（系统托盘中显示 Docker 图标）

## 部署步骤

### 1. 启动 Docker Desktop
```powershell
# 启动 Docker Desktop（如果未运行）
Start-Process "C:\Program Files\Docker\Docker\Docker Desktop.exe"
```

等待 2-3 分钟让 Docker Desktop 完全启动。

### 2. 验证 Docker 状态
```powershell
# 检查 Docker 版本
docker version

# 检查 Docker 是否正常运行
docker ps
```

### 3. 启动 FastGPT 服务
```powershell
# 进入部署目录
cd fastgpt-deploy

# 启动所有服务（后台运行）
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看服务日志
docker-compose logs -f
```

## 服务访问

启动成功后，可以通过以下地址访问：

- **FastGPT 主服务**: http://localhost:3000
- **MinIO 控制台**: http://localhost:9001
- **FastGPT MCP 服务**: http://localhost:3005

## 默认登录信息

- **用户名**: root
- **密码**: 1234

## 常用命令

```powershell
# 查看所有容器状态
docker-compose ps

# 查看服务日志
docker-compose logs [服务名]

# 停止所有服务
docker-compose down

# 重启服务
docker-compose restart

# 更新服务
docker-compose pull
docker-compose up -d
```

## 故障排除

### 1. Docker Desktop 未启动
- 手动启动 Docker Desktop
- 等待完全启动后再执行部署命令

### 2. 端口冲突
- 检查端口 3000, 9000, 9001, 3005 是否被占用
- 修改 docker-compose.yml 中的端口映射

### 3. 镜像下载失败
- 检查网络连接
- 可以使用阿里云镜像（取消 docker-compose.yml 中阿里云镜像的注释）

### 4. 服务启动失败
```powershell
# 查看详细错误日志
docker-compose logs [服务名]

# 重新构建并启动
docker-compose down
docker-compose up -d --force-recreate
```

## 配置说明

- `docker-compose.yml`: 主要的服务配置文件
- `config.json`: FastGPT 的配置文件
- 数据持久化目录：
  - `./pg/data`: PostgreSQL 数据
  - `./mongo/data`: MongoDB 数据
  - `./redis/data`: Redis 数据
  - `./fastgpt-minio`: MinIO 文件存储

## 注意事项

1. 首次启动需要下载多个 Docker 镜像，可能需要较长时间
2. 确保系统有足够的磁盘空间（建议至少 10GB）
3. 建议在生产环境中修改默认密码和密钥
4. 定期备份数据目录
