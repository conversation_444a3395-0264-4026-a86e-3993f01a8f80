@echo off
echo ========================================
echo        启动 FastGPT 服务
echo ========================================
echo.

echo 正在检查 Docker 状态...
docker version >nul 2>&1
if %errorlevel% neq 0 (
    echo [错误] Docker 未运行，请先启动 Docker Desktop
    echo 正在尝试启动 Docker Desktop...
    start "" "C:\Program Files\Docker\Docker\Docker Desktop.exe"
    echo 请等待 Docker Desktop 启动完成后重新运行此脚本
    pause
    exit /b 1
)

echo [成功] Docker 正在运行
echo.

echo 正在启动 FastGPT 服务...
docker-compose up -d

echo.
echo 正在检查服务状态...
docker-compose ps

echo.
echo ========================================
echo        FastGPT 服务启动完成！
echo ========================================
echo.
echo 访问地址：
echo   FastGPT 主页: http://localhost:3000
echo   MinIO 控制台: http://localhost:9001
echo.
echo 默认登录信息：
echo   用户名: root
echo   密码: 1234
echo.
echo 按任意键退出...
pause >nul
